import fs from "fs";
import path from "path";
import Papa from "papapar<PERSON>";

const MAIN_CATEGORIES = ["Laptops", "Headphones", "Printers", "Mouse", "Storage"];
const KNOWN_BRANDS = [
  "HP",
  "Lenovo",
  "Dell",
  "Acer",
  "JBL",
  "Sony",
  "Logitech",
  "Epson",
  "Canon",
  "Sandisk",
  "SanDisk",
  "Seagate",
  "Toshiba",
];

function inferCategory(rawCategory, productName = "") {
  const name = (productName || "").toLowerCase();
  const raw = (rawCategory || "").toLowerCase();

  const isPrinters =
    /printer|toner|ink|cartridge|laserjet|officejet|deskjet/.test(name) ||
    /printer|toner|ink|cartridge/.test(raw);
  if (isPrinters) return "Printers";

  const isLaptops = /laptop|notebook|thinkpad|probook|ideapad|macbook/.test(name) || /laptop/.test(raw);
  if (isLaptops) return "Laptops";

  const isHeadphones = /headphone|headset|earbud|earphone|buds/.test(name) || /headphones?/.test(raw);
  if (isHeadphones) return "Headphones";

  const isMouse = /mouse|m185|m331|g\d{2}/.test(name) || /mouse/.test(raw);
  if (isMouse) return "Mouse";

  const isStorage = /ssd|hdd|hard drive|flash|usb|memory card|micro\s?sd|sd\s?card|nvme/.test(name) ||
    /storage|ssd|hdd|drive|usb/.test(raw);
  if (isStorage) return "Storage";

  // If raw category already matches one of main categories
  const match = MAIN_CATEGORIES.find((c) => c.toLowerCase() === raw);
  if (match) return match;

  return null; // unknown
}

function inferBrand(rawBrand, productName = "") {
  if (rawBrand && String(rawBrand).trim()) return String(rawBrand).trim();
  const upper = (productName || "").toUpperCase();
  const found = KNOWN_BRANDS.find((b) => upper.includes(b.toUpperCase()));
  return found || "";
}

export async function getProducts() {
  const file = path.join(process.cwd(), "data", "Sailand_Master_Product_Catalog.csv");
  const csv = fs.readFileSync(file, "utf8");
  const parsed = Papa.parse(csv, { header: true, skipEmptyLines: true });

  const rows = parsed.data.filter((p) => p && (p["Product Name"] || p.name));

  const products = rows.map((row, idx) => {
    const name = row["Product Name"] || row.name || String(row[Object.keys(row)[0]] || "").trim();
    const brandRaw = row["Brand"] || row.brand;
    const categoryRaw = row["Category"] || row.category;
    const brand = inferBrand(brandRaw, name);
    const category = inferCategory(categoryRaw, name);

    return {
      id: idx + 1,
      name: name || "Untitled Product",
      brand: brand || "",
      category: category || "",
      sheet: categoryRaw || "",
    };
  });

  return products;
}
