"use client";

import { useEffect } from "react";

export default function ScrollToResults({ searchQuery }) {
  useEffect(() => {
    if (!searchQuery) return;

    const targetId = "products-section";
    const element = document.getElementById(targetId);
    if (!element) return;

    const headerOffset = 120; // keep content clear of the sticky header
    const targetTop = element.getBoundingClientRect().top + window.scrollY - headerOffset;

    window.scrollTo({
      top: Math.max(targetTop, 0),
      behavior: "smooth",
    });
  }, [searchQuery]);

  return null;
}
