"use client";

import Link from "next/link";

function initials(name) {
  if (!name) return "?";
  const parts = name.split(/\s+/).filter(Boolean);
  if (parts.length === 1) {
    return parts[0][0].toUpperCase();
  }
  return `${parts[0][0]}${parts[parts.length - 1][0]}`.toUpperCase();
}

export default function TrustedBrandsMarquee({ items, selectedBrand }) {
  if (!items || items.length === 0) {
    return (
      <div className="rounded-2xl border border-slate-100 bg-white px-6 py-5 text-slate-500 shadow-sm">
        Brand highlights will appear once catalogue data is available.
      </div>
    );
  }

  const duplicated = [...items, ...items];

  return (
    <div className="rounded-2xl border border-slate-100 bg-white px-6 py-6 text-slate-800 shadow-sm">
      <div className="text-center">
        <p className="text-xs font-semibold uppercase tracking-[0.4em] text-cyan-600">Trusted Brands</p>
        <p className="mt-2 text-sm text-slate-600">
          Tap a partner to filter the catalogue. Hover to pause the carousel.
        </p>
      </div>

      <div className="relative mx-auto mt-4 w-full max-w-4xl overflow-hidden">
        <div className="pointer-events-none absolute inset-y-0 left-0 w-10 bg-gradient-to-r from-white via-white/80 to-transparent" />
        <div className="pointer-events-none absolute inset-y-0 right-0 w-10 bg-gradient-to-l from-white via-white/80 to-transparent" />
        <div className="flex w-max gap-3 animate-marquee hover:[animation-play-state:paused]">
          {duplicated.map((brandItem, index) => {
            const active = selectedBrand === brandItem.name;
            return (
              <Link
                key={`${brandItem.name}-${index}`}
                href={brandItem.href}
                className={`group inline-flex min-w-[180px] items-center gap-3 rounded-full border px-4 py-2 text-sm font-semibold transition ${
                  active
                    ? "border-cyan-500 bg-cyan-100 text-cyan-800 shadow-sm"
                    : "border-cyan-200 bg-white text-slate-700 hover:border-cyan-400 hover:bg-cyan-50 hover:text-cyan-700"
                }`}
              >
                <span
                  className={`flex h-8 w-8 items-center justify-center rounded-full text-sm font-semibold ${
                    active ? "bg-cyan-500 text-white" : "bg-cyan-100 text-cyan-700"
                  }`}
                >
                  {initials(brandItem.name)}
                </span>
                <span className="truncate">{brandItem.name}</span>
              </Link>
            );
          })}
        </div>
      </div>

      <style jsx>{`
        @keyframes marquee {
          from {
            transform: translateX(0);
          }
          to {
            transform: translateX(-50%);
          }
        }
        .animate-marquee {
          animation: marquee 22s linear infinite;
        }
      `}</style>
    </div>
  );
}
