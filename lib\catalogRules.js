export const BRAND_ALIASES = {
  HP: "HP",
  "Hewlett Packard": "HP",
  "HEWLETT-PACKARD": "HP",
  LENOVO: "Lenovo",
  LOGITECH: "Logitech",
  JBL: "JBL",
  SANDISK: "SanDisk",
  "SAN DISK": "SanDisk",
  EPSON: "Epson",
  KASPERSKY: "<PERSON><PERSON>sky",
  "ORI AMO": "Oraimo",
  ORIAMO: "Oraimo",
  ORAIMO: "Orai<PERSON>",
  PIXOR: "Pixor",
  ATEAM: "ATEAM",
  BAI: "BAI",
  UPS: "UPS",
  "HDD & SSD": "HDD & SSD",
  HDD: "HDD & SSD",
  "SPARK ACCESSORIES": "Spark Accessories",
  SPARK: "Spark Accessories",
  "LENOVO ACCESSORIES": "Lenovo",
  ACCESSORIES: "Accessories",
  ACCESORIES: "Accessories",
  "EPSON PRINTERS": "<PERSON>pson",
  "HP TONERS & CAT": "HP",
  "EPSON CONSUMABLES": "<PERSON>pson",
  "PIXOR POWERBANKS": "Pixor",
  "SAILAND TECHNOLOGY LTD": "Sailand Technology",
};

export function normalizeBrand(brandOrSheet = "", name = "") {
  const raw = (brandOrSheet || "").toString().trim().toUpperCase();
  if (BRAND_ALIASES[raw]) return BRAND_ALIASES[raw];

  const n = (name || "").toUpperCase();
  const hints = [
    ["HP", "HP"],
    ["HEWLETT", "HP"],
    ["LENOVO", "Lenovo"],
    ["THINKPAD", "Lenovo"],
    ["LOGITECH", "Logitech"],
    ["JBL", "JBL"],
    ["SANDISK", "SanDisk"],
    ["EPSON", "Epson"],
    ["KASPERSKY", "Kaspersky"],
    ["ORAIMO", "Oraimo"],
    ["PIXOR", "Pixor"],
    ["ATEAM", "ATEAM"],
    ["BAI", "BAI"],
  ];
  for (const [token, norm] of hints) if (n.includes(token)) return norm;

  return raw || "Unknown";
}

export const CATEGORY_ORDER = [
  "Computers",
  "Accessories",
  "Storage",
  "Printers & Consumables",
  "Power & Backup",
  "Audio",
  "Mice & Keyboards",
  "Memory & RAM",
  "Security / Software",
  "Other",
];

export function inferCategoryFromSheet(sheet = "") {
  const s = (sheet || "").toUpperCase();
  if (s.includes("EPSON PRINTERS")) return "Printers & Consumables";
  if (s.includes("HP TONERS") || s.includes("CONSUMABLES") || s.includes("BAI")) return "Printers & Consumables";
  if (s.includes("UPS") || s.includes("POWERBANK")) return "Power & Backup";
  if (s.includes("SANDISK") || s.includes("HDD") || s.includes("SSD")) return "Storage";
  if (s.includes("KASPERSKY")) return "Security / Software";
  if (s.includes("RAM")) return "Memory & RAM";
  if (s.includes("ORIAMO")) return "Accessories";
  if (s.includes("LOGITECH") || s.includes("ACCESSORIES") || s.includes("LENOVO ACCESSORIES") || s.includes("SPARK")) return "Accessories";
  if (s.includes("JBL")) return "Audio";
  if (s === "HP" || s === "LENOVO" || s === "ATEAM") return "Computers";
  return "";
}

export function inferCategoryFromName(name = "") {
  const n = (name || "").toUpperCase();
  if (/(PRINTER|LASERJET|DESKJET|OFFICEJET|ECOTANK|L\d{3,4}|MFP|CARTRIDGE|TONER|\\bINK\\b|INK TANK|RIBBON|DRUM|CONSUMABLE)/.test(n))
    return "Printers & Consumables";
  if (/(LAPTOP|NOTEBOOK|THINKPAD|IDEAPAD|PROBOOK|ELITEBOOK|PAVILION|DESKTOP|PC|TOWER)/.test(n))
    return "Computers";
  if (/(SSD|HDD|NVME|M\.2|SATA|FLASH|USB\s?DRIVE|MICROSD|MEMORY\s?CARD)/.test(n))
    return "Storage";
  if (/(RAM|DDR2|DDR3|DDR4|DDR5|SODIMM|DIMM)/.test(n))
    return "Memory & RAM";
  if (/(HEADPHONE|HEADSET|EARBUD|EARPHONE|SPEAKER|SOUNDBAR|TUNE|FLIP|CHARGE|PARTYBOX|QUANTUM)/.test(n))
    return "Audio";
  if (
    /(MOUSE|TRACKBALL|KEYBOARD|WIRELESS\s+COMBO|\bMK\d{2,3}\b|\bK\d{2,3}\b|\bM\d{2,3}\b|MX\s?(MASTER|ANYWHERE)|PEBBLE|POP\s+MOUSE|POP\s+KEYS|\bG\d{3}\b|G PRO|PRO\s+X)/i.test(
      name
    )
  )
    return "Mice & Keyboards";
  if (/(POWER\s?BANK|POWERBANK|MAH\b|UPS|BACKUP|INVERTER)/.test(n))
    return "Power & Backup";
  if (/(KASPERSKY|ANTIVIRUS|SECURITY|LICENSE|KEY\s?CODE)/.test(n))
    return "Security / Software";
  if (
    /(CABLE|ADAPTER|ADAPTOR|HUB|CASE|COVER|CHARGER|DOCK|STAND|MOUSEPAD|SCREEN\s?PROTECTOR|WEB(CAM| CAMERA)|MICROPHONE|CAR\s?MOUNT|SMART\s?WATCH|SMARTWATCH|NECKLACE|CLIPPER|TRIMMER|POWER\s?EXTENSION)/.test(
      n
    )
  )
    return "Accessories";
  if (/(TWS|EARBUD|EARPOD|EARPHONE|EAR BUD|EAR POD)/.test(n)) return "Audio";
  if (/(BATTERY)/.test(n)) return "Power & Backup";
  return "";
}

export function inferCategory({ sheet, name }) {
  const byName = inferCategoryFromName(name);
  if (byName) return byName;
  const bySheet = inferCategoryFromSheet(sheet);
  if (bySheet) return bySheet;
  return "Other";
}

export function slugify(s) {
  return (s || "")
    .toString()
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, "-")
    .replace(/(^-|-$)+/g, "");
}
