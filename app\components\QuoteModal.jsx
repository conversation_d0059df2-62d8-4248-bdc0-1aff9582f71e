"use client";

import { useEffect, useState } from "react";

export default function QuoteModal({ product, open, onClose, onConfirm }) {
  const [qty, setQty] = useState(1);

  useEffect(() => {
    if (open) {
      setQty(1);
    }
  }, [open]);

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-slate-900/40 p-4">
      <div className="w-full max-w-md rounded-2xl bg-white p-6 shadow-xl">
        <h3 className="text-xl font-semibold text-slate-900">Add to Quote</h3>
        <p className="mt-2 text-sm text-slate-500">
          {product?.name} - {product?.brand} ({product?.category})
        </p>
        <label className="mt-6 block text-sm font-medium text-slate-700">Quantity</label>
        <input
          type="number"
          min={1}
          value={qty}
          onChange={(e) => setQty(Math.max(1, Number(e.target.value) || 1))}
          className="mt-1 w-full rounded-lg border border-slate-200 px-3 py-2 focus:border-cyan-500 focus:outline-none focus:ring-2 focus:ring-cyan-100"
        />
        <div className="mt-6 flex justify-end gap-3">
          <button
            type="button"
            onClick={onClose}
            className="rounded-lg border border-slate-200 px-4 py-2 text-sm font-semibold text-slate-600 transition hover:border-slate-300 hover:text-slate-700"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={() => {
              onConfirm(qty);
              onClose();
            }}
            className="rounded-lg bg-cyan-500 px-4 py-2 text-sm font-semibold text-white shadow-sm transition hover:bg-cyan-400"
          >
            Add
          </button>
        </div>
      </div>
    </div>
  );
}
