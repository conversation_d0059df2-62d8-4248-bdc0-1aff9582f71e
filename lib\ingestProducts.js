import fs from "fs";
import path from "path";
import { normalizeBrand, inferCategory, slugify } from "./catalogRules.js";

function normalizeCategoryRaw(raw = "", name = "", brand = "") {
  const combined = `${raw} ${name} ${brand}`.toUpperCase();
  const b = brand.toUpperCase();

  if (/(PRINTER|TONER|CARTRIDGE|\bINK\b|INK TANK|DRUM|CONSUMABLE)/.test(combined)) return "Printers & Consumables";
  if (/(LAPTOP|NOTEBOOK|MACBOOK|SURFACE|DESKTOP|AIO|ALL[- ]IN[- ]ONE|WORKSTATION|VOSTRO|THINKCENTRE|PROBOOK|ELITEBOOK)/.test(combined))
    return "Computers";
  if (b === "SANDISK" || /(\bSSD\b|NVME|M\.2)/.test(combined)) return "Storage";
  if (/(APC|UPS|POWER\s?BACKUP|INVERTER)/.test(combined)) return "Power & Backup";
  if (/(HEADSET|EARBUD|EARPHONE|SPEAKER|JABRA|POLY|CONFERENCE|LOGITECH C\d|WEBCAM)/.test(combined)) return "Audio";
  if (/(MOUSE|TRACKBALL|KEYBOARD|WIRELESS\s+COMBO|\bMK\d{2,3}\b|\bK\d{2,3}\b|\bM\d{2,3}\b|MX\s?(MASTER|ANYWHERE)|PEBBLE|POP\s+MOUSE|POP\s+KEYS|\bG\d{3}\b)/i.test(
    name
  ))
    return "Mice & Keyboards";
  if (/(KASPERSKY|ANTIVIRUS|LICENSE|SECURITY)/.test(combined)) return "Security / Software";
  if (/(BACKPACK|BAG|SLEEVE|CASE|DOCK|ADAPTER|ADAPTOR|HUB|CABLE|PROJECTOR|ROUTER|TP-LINK|STARLINK)/.test(combined))
    return "Accessories";

  return "";
}

function parseSailandPriceList(json = {}) {
  const products = [];

  for (const [section, entries] of Object.entries(json)) {
    if (!Array.isArray(entries)) continue;
    if (section === "SAILAND TECHNOLOGY LTD") continue;
    const sheet = (section || "").trim();
    const sectionKey = sheet;

    for (const item of entries) {
      if (!item || typeof item !== "object") continue;
      const nameCandidate =
        item[sectionKey] ||
        item.Description ||
        (typeof item.Column1 === "string" ? item.Column1 : undefined) ||
        (typeof item.Column3 === "string" ? item.Column3 : undefined);
      if (!nameCandidate) continue;
      const name = String(nameCandidate).trim();
      if (!name || name.startsWith("**") || /^description$/i.test(name)) continue;

      const priceValue =
        item.PRICE ??
        item.Price ??
        item.price ??
        (typeof item.Column2 === "number" ? item.Column2 : undefined) ??
        (typeof item.Column2 === "string" && !Number.isNaN(Number(item.Column2)) ? Number(item.Column2) : undefined);
      const price = Number.isFinite(Number(priceValue)) ? Number(priceValue) : undefined;
      const brand = normalizeBrand(sheet || "", name);
      const fromRaw = normalizeCategoryRaw(sheet, name, brand);
      const category = fromRaw || inferCategory({ sheet, name });

      products.push({
        name,
        brand,
        category,
        sheet,
        price,
      });
    }
  }

  return products;
}

function parseAitReseller(json = {}) {
  const products = [];

  for (const [section, entries] of Object.entries(json)) {
    if (!Array.isArray(entries)) continue;
    if (section.trim().toLowerCase() === "homepage") continue;

    const sectionKey = section.trim();

    for (const item of entries) {
      if (!item || typeof item !== "object") continue;

      const productNumber = item[section] ?? item[sectionKey] ?? item["PRODUCT NUMBER"] ?? item["Product Number"];
      const rawDesc = item.Column3 || item["ITEM DESCRIPTION"] || item.Description;
      const availability = item.Column4 || item.Status || item.Availability;
      const priceVal = item.Column5 ?? item.Price ?? item.PRICE;

      if (rawDesc && /item description/i.test(String(rawDesc))) continue;

      const nameParts = [productNumber, rawDesc].filter(Boolean).map((s) => String(s).trim()).filter(Boolean);
      const name = nameParts.join(" - ") || sectionKey;
      if (!name || name.toUpperCase().includes("PRODUCT NUMBER") || /^DESCRIPTION$/i.test(name.trim())) continue;

      const sheet = (item.Column11 || sectionKey || "").toString().trim();
      const brand = normalizeBrand(sectionKey || "", name);
      const fromRaw = normalizeCategoryRaw(sheet, name, brand);
      const category = fromRaw || inferCategory({ sheet, name });
      const price = Number.isFinite(Number(priceVal)) ? Number(priceVal) : undefined;

      products.push({
        name,
        brand,
        category,
        sheet,
        availability,
        price,
      });
    }
  }

  return products;
}

export async function loadProductsFromCsv() {
  const dataDir = path.join(process.cwd(), "data");
  const sailandPath = path.join(dataDir, "sailand-price-list.json");
  const aitPath = path.join(dataDir, "ait-reseller-feb-2026.json");

  const sailandJson = fs.existsSync(sailandPath) ? JSON.parse(fs.readFileSync(sailandPath, "utf8")) : {};
  const aitJson = fs.existsSync(aitPath) ? JSON.parse(fs.readFileSync(aitPath, "utf8")) : {};

  const combined = [...parseSailandPriceList(sailandJson), ...parseAitReseller(aitJson)];

  const seen = new Set();
  const unique = [];

  for (const product of combined) {
    if (!product.name) continue;
    const baseSlug = slugify(`${product.brand || "unknown"}-${product.name}`);
    const slug = baseSlug || slugify(product.name);
    if (seen.has(slug)) continue;
    seen.add(slug);
    unique.push({
      ...product,
      id: unique.length + 1,
      slug,
    });
  }

  return unique;
}

export function groupByCategoryAndBrand(products) {
  const byCategory = {};
  for (const p of products) {
    if (!byCategory[p.category]) byCategory[p.category] = {};
    if (!byCategory[p.category][p.brand]) byCategory[p.category][p.brand] = [];
    byCategory[p.category][p.brand].push(p);
  }
  return byCategory;
}
