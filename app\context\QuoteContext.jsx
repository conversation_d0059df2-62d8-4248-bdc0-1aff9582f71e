"use client";

import React, { createContext, useContext, useEffect, useMemo, useState } from "react";

const QuoteContext = createContext(null);

export function useQuote() {
  return useContext(QuoteContext);
}

export default function QuoteProvider({ children }) {
  const [items, setItems] = useState([]);
  const [customer, setCustomer] = useState({ name: "", phone: "", email: "", notes: "" });

  useEffect(() => {
    try {
      const saved = JSON.parse(localStorage.getItem("sailand:quote") || "{}");
      if (saved.items) setItems(saved.items);
      if (saved.customer) setCustomer(saved.customer);
    } catch {
      // ignore malformed storage
    }
  }, []);

  useEffect(() => {
    localStorage.setItem("sailand:quote", JSON.stringify({ items, customer }));
  }, [items, customer]);

  function addItem(item, qty = 1) {
    setItems((prev) => {
      const idx = prev.findIndex((p) => p.slug === item.slug);
      if (idx >= 0) {
        const copy = [...prev];
        copy[idx] = { ...copy[idx], qty: copy[idx].qty + qty };
        return copy;
      }
      return [...prev, { ...item, qty }];
    });
  }

  function updateQty(slug, qty) {
    setItems((prev) =>
      prev.map((p) => (p.slug === slug ? { ...p, qty: Math.max(1, Number(qty) || 1) } : p))
    );
  }

  function removeItem(slug) {
    setItems((prev) => prev.filter((p) => p.slug !== slug));
  }

  function clearAll() {
    setItems([]);
  }

  const value = useMemo(
    () => ({
      items,
      addItem,
      updateQty,
      removeItem,
      clearAll,
      customer,
      setCustomer,
    }),
    [items, customer]
  );

  return <QuoteContext.Provider value={value}>{children}</QuoteContext.Provider>;
}

