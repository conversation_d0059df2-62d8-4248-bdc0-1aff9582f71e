"use client";

import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useTransition } from "react";

export default function SortSelect({ options, value }) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();

  const handleChange = (event) => {
    const nextValue = event.target.value;
    const params = new URLSearchParams(searchParams);

    params.delete("page");

    if (nextValue === "default") {
      params.delete("sort");
    } else {
      params.set("sort", nextValue);
    }

    const href = `${pathname}${params.toString() ? `?${params.toString()}` : ""}`;

    startTransition(() => {
      router.push(href, { scroll: false });
    });
  };

  return (
    <select
      id="sort"
      name="sort"
      value={value}
      onChange={handleChange}
      disabled={isPending}
      className="mt-2 w-full rounded-full border border-slate-200 bg-slate-50 px-4 py-2.5 text-sm text-slate-600 shadow-sm transition focus:border-blue-500 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-100 disabled:cursor-wait"
    >
      {options.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  );
}
