Stack trace:
Frame         Function      Args
0007FFFFA8E0  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF97E0) msys-2.0.dll+0x1FEBA
0007FFFFA8E0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFABB8) msys-2.0.dll+0x67F9
0007FFFFA8E0  000210046832 (000210285FF9, 0007FFFFA798, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA8E0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFA8E0  0002100690B4 (0007FFFFA8F0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFABC0  00021006A49D (0007FFFFA8F0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF68070000 ntdll.dll
7FFF663A0000 KERNEL32.DLL
7FFF65790000 KERNELBASE.dll
7FFF67640000 USER32.dll
7FFF652B0000 win32u.dll
7FFF67610000 GDI32.dll
7FFF65400000 gdi32full.dll
7FFF65360000 msvcp_win.dll
7FFF65BE0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFF66750000 advapi32.dll
7FFF67F80000 msvcrt.dll
7FFF664D0000 sechost.dll
7FFF65530000 bcrypt.dll
7FFF67440000 RPCRT4.dll
7FFF648F0000 CRYPTBASE.DLL
7FFF652E0000 bcryptPrimitives.dll
7FFF67DF0000 IMM32.DLL
