﻿@import "tailwindcss";

:root {
  --background: #eef2ff;
  --foreground: #0f172a;
  --accent: #38BDF8;
  --primary: #2563EB;
  --primary-dark: #1E3A8A;
  --card: #ffffff;
  --text-light: #475569;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: linear-gradient(135deg, #f3f6ff 0%, #eef6ff 40%, #e6f1ff 100%);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

a {
  color: inherit;
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  text-decoration: underline;
}

.glass-card {
  background: rgba(255, 255, 255, 0.78);
  border: 1px solid rgba(255, 255, 255, 0.55);
  box-shadow: 0 8px 20px rgba(15, 23, 42, 0.08);
  backdrop-filter: blur(6px);
}

.glass-button {
  background: linear-gradient(135deg, #38bdf8 0%, #2563eb 55%, #1e3a8a 100%);
  color: #f8fbff !important;
  border: 1px solid rgba(255, 255, 255, 0.32);
  box-shadow: 0 10px 22px rgba(37, 99, 235, 0.28);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.18);
}

.glass-button:hover {
  filter: brightness(1.08);
}

.glass-button:active {
  transform: translateY(1px);
}

.glass-input {
  background: rgba(255, 255, 255, 0.75);
  border: 1px solid rgba(148, 163, 184, 0.5);
  backdrop-filter: blur(4px);
}

