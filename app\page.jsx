﻿"use client";

import Link from "next/link";
import { motion, useReducedMotion } from "framer-motion";

const METRICS = [
  { label: "Brands distributed", value: "35+" },
  { label: "Enterprise projects", value: "220" },
  { label: "Response time", value: "< 2 hrs" },
];

const FEATURED_CATEGORIES = [
  {
    title: "Computers & Workstations",
    description: "Powerful laptops, desktops, and workstations for hybrid teams and creative studios.",
    href: "/products?category=Computers",
  },
  {
    title: "Audio & Collaboration",
    description: "Headsets, conferencing cameras, and smart speakers for crystal-clear communication.",
    href: "/products?category=Audio",
  },
  {
    title: "Accessories & Peripherals",
    description: "Docking stations, storage, mice, and keyboards that complete every workstation.",
    href: "/products?category=Accessories",
  },
  {
    title: "Security & Software",
    description: "Endpoint protection, licenses, and backup solutions with localized support.",
    href: "/products?category=Security%20/%20Software",
  },
];

const fadeUp = {
  hidden: { opacity: 0, y: 16 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
};

const stagger = {
  hidden: {},
  visible: { transition: { staggerChildren: 0.08, delayChildren: 0.05 } },
};

const CheckIcon = (props) => (
  <svg viewBox="0 0 20 20" fill="none" aria-hidden="true" {...props}>
    <circle cx="10" cy="10" r="9" className="fill-cyan-100/80" />
    <path
      d="M6 10.5 8.5 13l5.5-6"
      className="stroke-cyan-700"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const ArrowRightIcon = (props) => (
  <svg viewBox="0 0 20 20" fill="none" aria-hidden="true" {...props}>
    <path
      d="M4 10h10m0 0-4-4m4 4-4 4"
      stroke="currentColor"
      strokeWidth="1.6"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default function HomePage() {
  const prefersReducedMotion = useReducedMotion();

  const motionProps = prefersReducedMotion
    ? {}
    : { initial: "hidden", whileInView: "visible", viewport: { once: true, amount: 0.2 } };

  return (
    <main className="min-h-screen bg-slate-50 text-slate-900">
      <section className="relative overflow-hidden bg-gradient-to-br from-blue-900 via-blue-700 to-cyan-500">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_top,_rgba(255,255,255,0.18),_transparent_60%)]" />
        <div className="absolute inset-0 opacity-50">
          <div className="absolute -left-24 top-20 h-40 w-40 rounded-full bg-cyan-400/30 blur-3xl" />
          <div className="absolute right-0 -bottom-10 h-48 w-48 rounded-full bg-blue-900/40 blur-3xl" />
        </div>
        <div className="relative z-10 mx-auto flex max-w-6xl flex-col gap-10 px-6 py-20 sm:px-8 lg:px-10">
          <motion.div
            {...motionProps}
            variants={stagger}
            className="flex flex-col gap-10"
          >
            <motion.div variants={fadeUp} className="max-w-2xl space-y-6 text-white">
              <span className="inline-flex items-center rounded-full border border-white/30 bg-white/5 px-3 py-1 text-xs font-semibold uppercase tracking-[0.3em] text-white/80">
                Sailand Technology LTD
              </span>
              <h1 className="text-4xl font-semibold leading-tight sm:text-[44px]">
                Kenya&apos;s premium ICT distribution partner for high-performing teams.
              </h1>
              <p className="text-base text-white/85">
                Source, deploy, and support your technology stack with a single, accountable partner. From workstations to
                collaboration suites and smart accessories, Sailand curates the tools that keep your business ahead.
              </p>
              <div className="flex flex-wrap gap-3">
                <Link
                  href="/products"
                  className="inline-flex items-center justify-center gap-2 rounded-full bg-cyan-300 px-6 py-2 text-sm font-semibold text-slate-900 shadow-sm transition hover:bg-cyan-200 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white/80"
                >
                  Browse Catalogue
                  <ArrowRightIcon className="h-4 w-4" />
                </Link>
                <Link
                  href="/contact"
                  className="inline-flex items-center justify-center rounded-full border border-cyan-100/70 bg-white/10 px-6 py-2 text-sm font-semibold text-white shadow-sm transition hover:bg-white/20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white/80"
                >
                  Request A Quote
                </Link>
              </div>
            </motion.div>
          </motion.div>

          <motion.div
            {...motionProps}
            variants={stagger}
            className="grid gap-4 sm:grid-cols-3 lg:grid-cols-3"
          >
            {METRICS.map((metric) => (
              <motion.div
                variants={fadeUp}
                key={metric.label}
                className="rounded-2xl border border-white/30 bg-white/10 px-5 py-4 text-white/95 backdrop-blur transition hover:-translate-y-0.5 hover:bg-white/15"
              >
                <p className="text-xs font-semibold uppercase tracking-[0.3em] text-white/80">{metric.label}</p>
                <p className="mt-2 text-3xl font-semibold">{metric.value}</p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      <section className="mx-auto max-w-6xl px-6 py-16 sm:px-8 lg:px-10">
        <motion.div
          {...motionProps}
          variants={stagger}
          className="space-y-6"
        >
          <motion.div variants={fadeUp} className="space-y-6">
            <span className="inline-flex items-center rounded-full border border-cyan-200 bg-cyan-100/60 px-3 py-1 text-xs font-semibold uppercase tracking-[0.35em] text-slate-900">
              End-to-End Delivery
            </span>
            <h2 className="text-3xl font-semibold text-slate-900">Built for IT leaders who need more than a supplier.</h2>
            <p className="text-base text-slate-600">
              We collaborate with CTOs, procurement managers, and operations teams to design resilient infrastructure
              roadmaps. Our team ensures every rollout, from laptops to security appliances, arrives configured, tested,
              and ready to perform.
            </p>
          </motion.div>

        </motion.div>
      </section>

      <section className="bg-white">
        <div className="mx-auto max-w-6xl px-6 py-16 sm:px-8 lg:px-10">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div>
              <span className="inline-flex items-center rounded-full border border-cyan-200 bg-cyan-100/60 px-3 py-1 text-xs font-semibold uppercase tracking-[0.35em] text-slate-900">
                Explore Solutions
              </span>
              <h2 className="mt-4 text-3xl font-semibold text-slate-900">Tailored collections for every team</h2>
              <p className="mt-2 max-w-2xl text-sm text-slate-600">
                Navigate curated product assortments that accelerate productivity, protect your assets, and deliver
                unforgettable client experiences.
              </p>
            </div>
            <Link
              href="/products"
              prefetch={false}
              className="inline-flex items-center justify-center gap-2 rounded-full border border-cyan-300 bg-cyan-100/70 px-5 py-2 text-sm font-semibold text-slate-900 transition hover:bg-cyan-200 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-cyan-400"
            >
              View full catalogue
              <ArrowRightIcon className="h-4 w-4" />
            </Link>
          </div>
          <motion.div
            {...motionProps}
            variants={stagger}
            className="mt-10 grid gap-6 md:grid-cols-2"
          >
            {FEATURED_CATEGORIES.map((category) => (
              <motion.div key={category.title} variants={fadeUp}>
                <Link
                  prefetch={false}
                  href={category.href}
                  className="group flex h-full flex-col justify-between rounded-3xl border border-slate-200 bg-slate-50 p-6 text-slate-900 shadow-sm transition hover:-translate-y-1 hover:border-cyan-300 hover:bg-white hover:shadow-lg focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-cyan-400"
                >
                  <div>
                    <h3 className="text-xl font-semibold">{category.title}</h3>
                    <p className="mt-3 text-sm text-slate-600">{category.description}</p>
                  </div>
                  <span className="mt-6 inline-flex items-center gap-1 text-sm font-semibold text-cyan-600 transition group-hover:text-cyan-700">
                    Explore category
                    <ArrowRightIcon className="h-4 w-4" />
                  </span>
                </Link>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      <section className="mx-auto max-w-6xl px-6 py-16 sm:px-8 lg:px-10">
        <motion.div
          {...motionProps}
          variants={stagger}
          className="rounded-3xl border border-slate-200 bg-white p-8 shadow-sm lg:p-10"
        >
          <div className="grid gap-8 lg:grid-cols-[1.4fr,1fr] lg:items-center">
            <motion.div variants={fadeUp} className="space-y-5">
              <h2 className="text-3xl font-semibold text-slate-900">Let&apos;s architect your next upgrade.</h2>
              <p className="text-base text-slate-600">
                Whether you&apos;re equipping a new floor, modernizing field agents, or revitalising your boardroom,
                Sailand delivers a premium buying experience from evaluation to post-deployment support.
              </p>
              <ul className="space-y-3 text-sm text-slate-600">
                <li className="flex items-start gap-3">
                  <CheckIcon className="mt-0.5 h-5 w-5" />
                  <span>Dedicated project teams for rollouts above 50 seats.</span>
                </li>
                <li className="flex items-start gap-3">
                  <CheckIcon className="mt-0.5 h-5 w-5" />
                  <span>Flexible financing, staged deliveries, and advance replacements.</span>
                </li>
                <li className="flex items-start gap-3">
                  <CheckIcon className="mt-0.5 h-5 w-5" />
                  <span>Insightful reporting to keep stakeholders aligned.</span>
                </li>
              </ul>
              <div className="flex flex-wrap gap-3">
                <Link
                  href="/contact"
                  className="inline-flex items-center justify-center gap-2 rounded-full bg-cyan-400 px-6 py-2 text-sm font-semibold text-slate-900 shadow-sm transition hover:bg-cyan-300 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-cyan-500"
                >
                  Book a discovery call
                  <ArrowRightIcon className="h-4 w-4" />
                </Link>
                <a
                  href="https://api.whatsapp.com/send?phone=254704556107"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center justify-center gap-2 rounded-full border border-cyan-300 bg-cyan-100/70 px-6 py-2 text-sm font-semibold text-slate-900 transition hover:bg-cyan-200 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-cyan-500"
                >
                  Chat on WhatsApp
                  <ArrowRightIcon className="h-4 w-4" />
                </a>
              </div>
            </motion.div>
            <motion.div variants={fadeUp} className="rounded-2xl border border-slate-200 bg-slate-50 p-6 shadow-inner">
              <h3 className="text-base font-semibold text-slate-900">Operational hours</h3>
              <p className="mt-2 text-sm text-slate-600">Monday - Friday: 8:00 AM - 5:30 PM (EAT)</p>
              <div className="mt-6 space-y-3 text-sm text-slate-600">
                <p className="font-semibold text-slate-900">Call</p>
                <a href="tel:+254704556107" className="text-cyan-600 transition hover:text-cyan-700">
                  +254 704 556107
                </a>
                <p className="font-semibold text-slate-900">Email</p>
                <a href="mailto:<EMAIL>" className="text-cyan-600 transition hover:text-cyan-700">
                  <EMAIL>
                </a>
                <p className="font-semibold text-slate-900">Visit</p>
                <span>Nextgen Mall, Mombasa Road, Nairobi</span>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </section>
    </main>
  );
}


