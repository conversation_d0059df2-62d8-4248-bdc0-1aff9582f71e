"use client";

import Link from "next/link";
import { useQuote } from "../context/QuoteContext";

export default function QuoteBadge() {
  const { items } = useQuote();
  const count = items.reduce((sum, item) => sum + (item.qty || 1), 0);

  return (
    <Link href="/quote" className="relative inline-flex items-center ml-4">
      <span className="rounded-lg border border-slate-200 px-3 py-2 text-sm transition hover:border-cyan-500">
        Request a Quote
      </span>
      {count > 0 && (
        <span className="absolute -right-2 -top-2 rounded-full bg-cyan-600 px-2 py-0.5 text-xs font-semibold text-white">
          {count}
        </span>
      )}
    </Link>
  );
}
