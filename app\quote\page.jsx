"use client";

import { useMemo } from "react";
import Link from "next/link";
import { useQuote } from "../context/QuoteContext";
import { downloadQuotePdf, shareQuotePdf } from "../../lib/generateQuotePdf";

const WHATSAPP_PHONE = "254704556107";
const WHATSAPP_BASE_URL = "https://api.whatsapp.com/send";
const formatPrice = (value) =>
  typeof value === "number"
    ? new Intl.NumberFormat("en-KE", { style: "currency", currency: "KES", minimumFractionDigits: 0 }).format(value)
    : null;

function buildWhatsAppUrl(message = "") {
  const params = new URLSearchParams({ phone: WHATSAPP_PHONE });
  if (message) {
    params.set("text", message);
  }
  return `${WHATSAPP_BASE_URL}?${params.toString()}`;
}

export default function QuotePage() {
  const { items, updateQty, removeItem, clearAll, customer, setCustomer } = useQuote();

  const quoteNumber = useMemo(() => {
    const d = new Date();
    return `Q-${d.getFullYear()}${String(d.getMonth() + 1).padStart(2, "0")}${String(d.getDate()).padStart(2, "0")}-${String(d.getHours()).padStart(2, "0")}${String(d.getMinutes()).padStart(2, "0")}`;
  }, []);

  function buildWhatsAppMessage({ includeAttachmentNote = false } = {}) {
    const summary = items.slice(0, 5).map((item) => `${item.name} x${item.qty}`).join("; ");
    const lines = [
      "Hello Sailand Technology LTD, I'd like a quote.",
      `Quote No: ${quoteNumber}`,
      `Items: ${summary || "None"}`,
      `Name: ${customer.name || "-"}`,
      `Phone: ${customer.phone || "-"}`,
      `Email: ${customer.email || "-"}`,
      `Notes: ${customer.notes || "-"}`,
    ];

    if (includeAttachmentNote) {
      lines.push("", "I've attached the PDF quote for your review.");
    }

    return lines.join("\n");
  }

  function openWhatsApp(message) {
    const url = buildWhatsAppUrl(message);
    window.open(url, "_blank", "noopener,noreferrer");
  }

  function handleDownloadPdf() {
    downloadQuotePdf({ items, customer, quoteNumber });
  }

  async function handleSharePdf() {
    const { shared } = await shareQuotePdf({
      items,
      customer,
      quoteNumber,
      shareText: `Quote ${quoteNumber} from Sailand Technology LTD`,
      shareTitle: `Sailand Technology Quote ${quoteNumber}`,
    });
    if (!shared) {
      downloadQuotePdf({ items, customer, quoteNumber });
      window.alert(
        "Sharing files directly isn't supported on this device. The PDF has been downloaded so you can attach it to WhatsApp manually."
      );
    }
  }

  function handleWhatsApp() {
    const message = buildWhatsAppMessage();
    openWhatsApp(message);
  }

  function handleWhatsAppWithPdf() {
    downloadQuotePdf({ items, customer, quoteNumber });
    const message = buildWhatsAppMessage({ includeAttachmentNote: true });
    window.setTimeout(() => openWhatsApp(message), 400);
  }

  const totals = useMemo(() => {
    const totalQty = items.reduce((sum, item) => sum + Number(item.qty || 0), 0);
    const subtotal = items.reduce((sum, item) => {
      if (typeof item.price !== "number") return sum;
      const qty = Number(item.qty || 0) || 0;
      return sum + item.price * qty;
    }, 0);
    return { totalQty, subtotal };
  }, [items]);

  return (
    <main className="mx-auto min-h-screen max-w-5xl px-4 py-10 text-slate-800 sm:px-6 lg:px-8">
      <div className="flex flex-wrap items-center justify-between gap-3">
        <div>
          <p className="text-xs font-semibold uppercase tracking-[0.35em] text-cyan-600">Quote Builder</p>
          <h1 className="mt-2 text-3xl font-semibold text-slate-900">Your Quote</h1>
        </div>
        <div className="flex items-center gap-2">
          <span className="rounded-full bg-cyan-50 px-4 py-1 text-sm font-semibold text-cyan-700">#{quoteNumber}</span>
          <Link
            href="/products"
            className="inline-flex items-center justify-center rounded-full border border-cyan-200 bg-white px-4 py-2 text-sm font-semibold text-cyan-700 shadow-sm transition hover:bg-cyan-50"
          >
            + Add products
          </Link>
        </div>
      </div>

      {items.length === 0 ? (
        <div className="mt-8 rounded-2xl border border-dashed border-cyan-200 bg-cyan-50 p-10 text-center">
          <h2 className="text-xl font-semibold text-slate-900">Your quote is empty</h2>
          <p className="mt-2 text-sm text-slate-600">
            Browse the catalogue and add items to build your procurement quote.
          </p>
          <Link
            href="/products"
            className="mt-6 inline-flex items-center justify-center rounded-full bg-cyan-500 px-6 py-2 text-sm font-semibold text-white shadow-sm transition hover:bg-cyan-400"
          >
            Browse Products
          </Link>
        </div>
      ) : (
        <div className="mt-8 grid gap-6 lg:grid-cols-[1.1fr,0.9fr]">
          <div className="space-y-4">
            <div className="flex items-center justify-between rounded-2xl border border-slate-100 bg-slate-50 px-4 py-3 text-sm font-semibold text-slate-700">
              <span>
                {items.length} line{items.length > 1 ? "s" : ""} · {totals.totalQty} unit{totals.totalQty !== 1 ? "s" : ""}
              </span>
              <button
                type="button"
                onClick={clearAll}
                className="text-sm font-semibold text-rose-500 transition hover:text-rose-600"
              >
                Clear all
              </button>
            </div>

            <div className="space-y-3">
              {items.map((item) => (
                <div
                  key={item.slug}
                  className="rounded-2xl border border-slate-100 bg-white p-4 shadow-sm transition hover:-translate-y-0.5 hover:border-cyan-200 hover:shadow"
                >
                  <div className="flex flex-col gap-3 md:flex-row md:items-start md:justify-between">
                    <div className="space-y-2">
                      <p className="text-sm font-semibold uppercase tracking-[0.3em] text-cyan-600">{item.category}</p>
                      <h3 className="text-lg font-semibold text-slate-900">{item.name}</h3>
                      <p className="text-sm text-slate-600">Brand: {item.brand}</p>
                      {typeof item.price === "number" && (
                        <p className="text-sm font-semibold text-slate-900">
                          {formatPrice(item.price)}{" "}
                          <span className="text-[11px] font-bold uppercase tracking-wide text-amber-700">Exclusive VAT</span>
                        </p>
                      )}
                    </div>
                    <div className="flex items-center gap-3">
                      <label className="text-xs font-semibold uppercase tracking-wide text-slate-500">
                        Qty
                        <input
                          type="number"
                          min={1}
                          value={item.qty}
                          onChange={(event) => updateQty(item.slug, event.target.value)}
                          className="mt-1 w-20 rounded-lg border border-slate-200 px-3 py-2 text-sm focus:border-cyan-500 focus:outline-none focus:ring-2 focus:ring-cyan-100"
                        />
                      </label>
                      {typeof item.price === "number" && (
                        <div className="text-right">
                          <p className="text-[11px] uppercase tracking-wide text-slate-400">Line total</p>
                          <p className="text-sm font-semibold text-slate-900">
                            {formatPrice(item.price * (Number(item.qty) || 1))}
                          </p>
                        </div>
                      )}
                      <button
                        type="button"
                        onClick={() => removeItem(item.slug)}
                        className="rounded-full border border-rose-100 bg-rose-50 px-3 py-2 text-sm font-semibold text-rose-600 transition hover:border-rose-200 hover:bg-rose-100"
                      >
                        Remove
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-4">
            <div className="rounded-2xl border border-slate-200 bg-white p-5 shadow-sm">
              <h2 className="text-lg font-semibold text-slate-900">Customer details</h2>
              <p className="mt-1 text-sm text-slate-600">We’ll include these on the PDF and WhatsApp message.</p>
              <div className="mt-4 grid gap-3">
                <input
                  type="text"
                  placeholder="Full name"
                  value={customer.name}
                  onChange={(event) => setCustomer({ ...customer, name: event.target.value })}
                  className="rounded-lg border border-slate-200 px-3 py-2 text-sm focus:border-cyan-500 focus:outline-none focus:ring-2 focus:ring-cyan-100"
                />
                <input
                  type="text"
                  placeholder="Phone (+254...)"
                  value={customer.phone}
                  onChange={(event) => setCustomer({ ...customer, phone: event.target.value })}
                  className="rounded-lg border border-slate-200 px-3 py-2 text-sm focus:border-cyan-500 focus:outline-none focus:ring-2 focus:ring-cyan-100"
                />
                <input
                  type="email"
                  placeholder="Email"
                  value={customer.email}
                  onChange={(event) => setCustomer({ ...customer, email: event.target.value })}
                  className="rounded-lg border border-slate-200 px-3 py-2 text-sm focus:border-cyan-500 focus:outline-none focus:ring-2 focus:ring-cyan-100"
                />
                <textarea
                  rows={3}
                  placeholder="Notes / requirements"
                  value={customer.notes}
                  onChange={(event) => setCustomer({ ...customer, notes: event.target.value })}
                  className="rounded-lg border border-slate-200 px-3 py-2 text-sm focus:border-cyan-500 focus:outline-none focus:ring-2 focus:ring-cyan-100"
                />
              </div>
            </div>

            {items.length > 0 && (
              <div className="rounded-2xl border border-cyan-100 bg-cyan-50 p-5 shadow-sm">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-base font-semibold text-slate-900">Order summary</h3>
                    <p className="text-xs font-semibold uppercase tracking-wide text-amber-700">Exclusive VAT</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-slate-600">Subtotal</p>
                    <p className="text-xl font-semibold text-slate-900">{formatPrice(totals.subtotal) || "—"}</p>
                  </div>
                </div>
              </div>
            )}

            <div className="rounded-2xl border border-cyan-100 bg-cyan-50 p-5 shadow-sm">
              <h3 className="text-base font-semibold text-slate-900">Share / export</h3>
              <p className="mt-1 text-sm text-slate-600">Send the quote to Sailand or save it for later.</p>
              <div className="mt-4 grid gap-3">
                <button
                  type="button"
                  onClick={handleDownloadPdf}
                  className="inline-flex items-center justify-center rounded-full bg-cyan-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm transition hover:bg-cyan-400"
                >
                  Download PDF
                </button>
                <button
                  type="button"
                  onClick={handleSharePdf}
                  className="inline-flex items-center justify-center rounded-full border border-cyan-400 bg-white px-4 py-2.5 text-sm font-semibold text-cyan-700 transition hover:bg-cyan-50"
                >
                  Share PDF
                </button>
                <button
                  type="button"
                  onClick={handleWhatsAppWithPdf}
                  className="inline-flex items-center justify-center rounded-full border border-cyan-400 bg-white px-4 py-2.5 text-sm font-semibold text-cyan-700 transition hover:bg-cyan-50"
                >
                  Send PDF via WhatsApp
                </button>
                <button
                  type="button"
                  onClick={handleWhatsApp}
                  className="inline-flex items-center justify-center rounded-full border border-cyan-400 bg-white px-4 py-2.5 text-sm font-semibold text-cyan-700 transition hover:bg-cyan-50"
                >
                  Send message via WhatsApp
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </main>
  );
}
