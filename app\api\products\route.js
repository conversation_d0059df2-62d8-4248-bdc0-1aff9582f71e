import { getProducts } from "../../../lib/getProducts";

export async function GET() {
  try {
    const items = await getProducts();
    return Response.json({ ok: true, items });
  } catch (err) {
    console.error("Failed to load products:", err);
    return new Response(JSON.stringify({ ok: false, error: "Failed to load products" }), {
      status: 500,
      headers: { "content-type": "application/json" },
    });
  }
}

