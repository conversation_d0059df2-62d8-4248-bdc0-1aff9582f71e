"use client";

import Link from "next/link";
import { useEffect, useState } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import QuoteBadge from "./QuoteBadge";

const NAV_LINKS = [
  { href: "/", label: "Home" },
  { href: "/products", label: "Shop" },
  { href: "/contact", label: "Contact" },
];

export default function Layout({ children }) {
  const pathname = usePathname();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [query, setQuery] = useState(() => searchParams.get("q") ?? "");

  useEffect(() => {
    const nextQuery = searchParams.get("q") ?? "";
    setQuery(nextQuery);
  }, [searchParams]);

  const handleSearchSubmit = (event) => {
    event.preventDefault();
    const trimmed = query.trim();
    const params = new URLSearchParams(searchParams);

    params.delete("page");

    if (trimmed) {
      params.set("q", trimmed);
    } else {
      params.delete("q");
    }

    const href = `/products${params.toString() ? `?${params.toString()}` : ""}`;
    router.push(href);
  };

  const isActive = (href) => {
    if (href === "/") {
      return pathname === "/";
    }
    return pathname.startsWith(href);
  };

  return (
    <div className="min-h-screen bg-transparent text-slate-900">
      <header className="glass-card fixed inset-x-0 top-0 z-40 border-b border-white/50 bg-white/95">
        <div className="mx-auto flex max-w-6xl items-center gap-4 px-4 py-2 sm:px-6 lg:px-8">
          <Link href="/" className="flex items-center gap-2">
            <span className="flex h-9 w-9 items-center justify-center rounded-full bg-gradient-to-br from-blue-700 to-cyan-400 text-sm font-semibold leading-none text-white shadow-[0_8px_18px_rgba(37,99,235,0.22)]">
              ST
            </span>
            <div className="leading-tight">
              <span className="block text-base font-semibold tracking-tight text-slate-900">
                Sailand Technology LTD
              </span>
              <span className="block text-[11px] font-medium uppercase tracking-[0.2em] text-cyan-600">
                ICT Distribution
              </span>
            </div>
          </Link>

          <nav className="hidden items-center gap-2 text-sm font-semibold text-slate-600 sm:flex">
            {NAV_LINKS.map((item) => {
              const active = isActive(item.href);
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`rounded-full px-3 py-1 transition ${
                    active ? "bg-cyan-100 text-cyan-700 shadow-sm" : "hover:text-cyan-600"
                  }`}
                >
                  {item.label}
                </Link>
              );
            })}
            <QuoteBadge />
          </nav>

          <form
            onSubmit={handleSearchSubmit}
            className="relative mx-auto flex-1 max-w-2xl"
          >
            <input
              type="search"
              placeholder="Search the catalogue..."
              value={query}
              onChange={(event) => setQuery(event.target.value)}
              className="glass-input w-full rounded-full border px-4 pr-11 py-2 text-sm text-slate-700 shadow-sm transition focus:border-cyan-500 focus:bg-white/95 focus:outline-none focus:ring-2 focus:ring-cyan-100"
              aria-label="Search catalogue"
            />
            <button
              type="submit"
              className="absolute right-2 top-1/2 -translate-y-1/2 rounded-full p-1.5 text-slate-500 transition hover:text-cyan-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-cyan-400"
              aria-label="Submit search"
            >
              <svg
                className="h-4 w-4"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M9.167 3.333a5.833 5.833 0 1 1 0 11.667 5.833 5.833 0 0 1 0-11.667Zm0 1.667a4.167 4.167 0 1 0 0 8.333 4.167 4.167 0 0 0 0-8.333Zm6.45 9.117 2.6 2.6-1.175 1.175-2.6-2.6 1.175-1.175Z"
                  fill="currentColor"
                />
              </svg>
            </button>
          </form>

          <div className="flex items-center gap-2">
            <Link
              href="/contact"
              className="glass-button inline-flex items-center justify-center rounded-full px-4 py-1.75 text-sm font-semibold text-white shadow-[0_8px_18px_rgba(56,189,248,0.24)] transition hover:brightness-110"
            >
              Request A Quote
            </Link>
          </div>
        </div>
      </header>

      <main className="pb-16 pt-24">{children}</main>

      <footer className="border-t border-slate-200 bg-slate-900 text-white">
        <div className="mx-auto max-w-6xl px-4 py-8 text-center text-sm text-slate-400 sm:px-6 lg:px-8">
          <p>&copy; {new Date().getFullYear()} Sailand Technology LTD. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
}
