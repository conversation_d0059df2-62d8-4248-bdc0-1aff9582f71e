﻿import { loadProductsFromCsv } from "../../../lib/ingestProducts";

export default async function AuditPage() {
  const products = await loadProductsFromCsv();
  const unmapped = products.filter((p) => p.category === "Other");

  return (
    <div className="min-h-screen bg-white text-gray-900">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <h1 className="text-2xl font-semibold mb-2">Audit: Unmapped Products</h1>
        <p className="text-sm text-gray-600 mb-6">Review products that fell into the "Other" category and adjust naming or rules.</p>

        <div className="overflow-x-auto border border-gray-200 rounded-xl">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-2 text-left text-xs font-semibold text-gray-600">#</th>
                <th className="px-4 py-2 text-left text-xs font-semibold text-gray-600">Name</th>
                <th className="px-4 py-2 text-left text-xs font-semibold text-gray-600">Brand</th>
                <th className="px-4 py-2 text-left text-xs font-semibold text-gray-600">Sheet</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
              {unmapped.map((p, i) => (
                <tr key={`${p.id}-${i}`} className="hover:bg-gray-50">
                  <td className="px-4 py-2 text-sm text-gray-700">{i + 1}</td>
                  <td className="px-4 py-2 text-sm text-gray-900">{p.name}</td>
                  <td className="px-4 py-2 text-sm text-gray-900">{p.brand}</td>
                  <td className="px-4 py-2 text-sm text-gray-600">{p.sheet}</td>
                </tr>
              ))}
              {unmapped.length === 0 && (
                <tr>
                  <td colSpan={4} className="px-4 py-6 text-center text-sm text-gray-600">All products are mapped.</td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}





