import Link from "next/link";
import { notFound } from "next/navigation";
import QuoteAddButton from "../QuoteAddButton";
import { loadProductsFromCsv } from "../../../lib/ingestProducts";

function buildWhatsappUrl(productName) {
  const params = new URLSearchParams({
    phone: "254704556107",
    text: `Hello Sailand Technology LTD, I'm interested in the ${productName}. Can you tell me more about it?`,
  });
  return `https://api.whatsapp.com/send?${params.toString()}`;
}

export default async function ProductDetailsPage({ params }) {
  const { slug } = params;
  const products = await loadProductsFromCsv();
  const product = products.find((p) => p.slug === slug);

  if (!product) {
    notFound();
  }

  const whatsappUrl = buildWhatsappUrl(product.name);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-100 via-white to-blue-50 text-[#1E1E1E]">
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <nav className="mb-6 text-sm text-[#6B7280]">
          <Link href="/products" className="hover:text-[#38BDF8]">Back to products</Link>
        </nav>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
          <div className="glass-card flex items-center justify-center rounded-3xl p-8">
            <div className="w-full aspect-square max-w-sm rounded-2xl bg-white/80 text-2xl font-semibold text-[#475569] shadow-inner backdrop-blur-sm">
              Placeholder Image
            </div>
          </div>

          <div className="flex flex-col justify-between">
            <div className="space-y-4">
              <div>
                <p className="text-sm uppercase tracking-[0.25em] text-[#2563EB]">{product.category}</p>
                <h1 className="text-3xl font-semibold text-[#0f172a] mt-1">{product.name}</h1>
              </div>
              <p className="text-lg text-[#1E1E1E]">Brand: {product.brand}</p>
              <p className="text-sm text-[#6B7280]">Catalogue Sheet: {product.sheet || "N/A"}</p>
              <p className="text-base text-[#6B7280] leading-relaxed">
                Detailed description for this product will be added soon. In the meantime, reach out to our team for
                up-to-date pricing, availability, and tailored recommendations.
              </p>
            </div>

            <div className="mt-8 flex flex-col gap-4">
              <QuoteAddButton product={product} />
              <a
                href={whatsappUrl}
                target="_blank"
                rel="noopener noreferrer"
                title="Chat with us on WhatsApp"
                className="glass-button inline-flex items-center justify-center gap-2 px-6 py-3 rounded-full font-semibold shadow-[0_12px_28px_rgba(37,99,235,0.26)] hover:-translate-y-0.5 transition-transform duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#2563EB]"
              >
                Chat with us on WhatsApp
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}



