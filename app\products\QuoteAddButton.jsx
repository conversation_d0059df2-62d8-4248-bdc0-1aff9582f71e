"use client";

import { useState } from "react";
import { useQuote } from "../context/QuoteContext";

export default function QuoteAddButton({ product, compact = false }) {
  const { addItem } = useQuote();
  const [qty, setQty] = useState(1);

  if (!product) return null;

  const handleAdd = () => {
    const normalized = Math.max(1, Number(qty) || 1);
    addItem(product, normalized);
    setQty(1);
  };

  return (
    <div className={`flex items-center ${compact ? "gap-2" : "gap-3"}`}>
      <label className="text-[11px] font-semibold uppercase tracking-wide text-slate-500">
        Qty
        <input
          type="number"
          min={1}
          value={qty}
          onChange={(event) => setQty(event.target.value)}
          className="mt-1 w-20 rounded-full border border-slate-200 bg-white px-3 py-1.5 text-sm text-slate-700 shadow-inner focus:border-cyan-500 focus:outline-none focus:ring-2 focus:ring-cyan-100"
        />
      </label>
      <button
        type="button"
        onClick={handleAdd}
        className="glass-button inline-flex items-center justify-center rounded-full px-5 py-2 text-sm font-semibold text-white shadow-[0_12px_30px_rgba(56,189,248,0.35)] transition active:scale-[0.99]"
      >
        Add to Quote
      </button>
    </div>
  );
}
