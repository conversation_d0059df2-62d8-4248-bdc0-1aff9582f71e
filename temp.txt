import Link from "next/link";
import { loadProductsFromCsv, groupByCategoryAndBrand } from "../../lib/ingestProducts";
import { CATEGORY_ORDER } from "../../lib/catalogRules";

const CATEGORY_EMOJI = {
  Computers: "\u{1F4BB}",
  Accessories: "\u{1F9F0}",
  Storage: "\u{1F5C3}",
  "Printers & Consumables": "\u{1F5A8}",
  "Power & Backup": "\u{1F50C}",
  "Security / Software": "\u{1F512}",
  Audio: "\u{1F3A7}",
  "Mice & Keyboards": "\u{1F5AF}",
  "Memory & RAM": "\u{1F9E9}",
  Other: "\u{1F4E6}",
};

export default async function ProductsPage({ searchParams }) {
  const products = await loadProductsFromCsv();
  const grouped = groupByCategoryAndBrand(products);
  const categoryTotals = Object.fromEntries(
    Object.entries(grouped).map(([cat, brands]) => [
      cat,
      Object.values(brands).reduce((total, items) => total + items.length, 0),
    ])
  );

  const selectedCategory = typeof searchParams?.category === "string" ? searchParams.category : null;
  const selectedBrand = typeof searchParams?.brand === "string" ? searchParams.brand : null;

  const allCategories = CATEGORY_ORDER;
  const otherCount = products.filter((p) => p.category === "Other").length;

  const filtered = products.filter((p) => {
    if (selectedCategory && selectedBrand) {
      return p.category === selectedCategory && p.brand === selectedBrand;
    }
    if (selectedCategory) {
      return p.category === selectedCategory;
    }
    if (selectedBrand) {
      return p.brand === selectedBrand;
    }
    return true;
  });

  const buildCategoryHref = (cat) => {
    if (selectedCategory === cat && !selectedBrand) return "/products";
    return `/products?category=${encodeURIComponent(cat)}`;
  };

  const buildBrandHref = (cat, brand) => {
    if (selectedCategory === cat && selectedBrand === brand) {
      return `/products?category=${encodeURIComponent(cat)}`;
    }
    return `/products?category=${encodeURIComponent(cat)}&brand=${encodeURIComponent(brand)}`;
  };

  return (
    <div className="min-h-screen bg-white text-[#1E1E1E]">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-semibold text-[#2563EB]">Products</h1>
            <p className="text-sm text-[#6B7280] mt-1">
              {selectedCategory || selectedBrand ? "Filtered results" : "All products"}
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Link href="/admin/audit" className="text-xs px-2.5 py-1 rounded-full bg-[#EFF6FF] text-[#2563EB] border border-[#2563EB]/30">
              Unmapped / Audit: {otherCount}
            </Link>
            {(selectedCategory || selectedBrand) && (
              <Link href="/products" className="text-sm px-3 py-1.5 rounded-md border border-[#2563EB]/40 text-[#2563EB] hover:bg-[#2563EB]/10">
                Clear Filters
              </Link>
            )}
          </div>

        {/* Mobile filters */}
        <div className="md:hidden mb-4">
          <details className="border border-[#E5E7EB] rounded-lg">
            <summary className="cursor-pointer px-3 py-2 text-sm font-medium">Filters</summary>
            <div className="p-3 space-y-2">
              {allCategories.map((cat) => (
                <div key={cat}>
                  <Link
                    href={buildCategoryHref(cat)}
                    className={`block w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                      selectedCategory === cat
                        ? "bg-[#DBEAFE] text-[#2563EB] font-semibold"
                        : "text-[#1E1E1E] hover:bg-[#EFF6FF]"
                    }`}
                  >
                    {cat}
                  </Link>
                  {selectedCategory === cat && (
                    <div className="mt-1 pl-3 space-y-1">
                      {Object.keys(grouped[cat] || {}).sort().map((brand) => (
                        <Link
                          key={brand}
                          href={buildBrandHref(cat, brand)}
                          className={`block w-full text-left px-2 py-1 rounded-md text-sm transition-colors ${
                            selectedBrand === brand
                              ? "bg-[#BFDBFE] text-[#2563EB] font-semibold"
                              : "text-[#1E1E1E] hover:bg-[#DBEAFE]"
                          }`}
                        >
                          {brand} ({(grouped[cat]?.[brand]?.length ?? 0)})
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </details>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-12 gap-6">
          {/* Sidebar */}
          <aside className="hidden md:block md:col-span-3 lg:col-span-3 h-full">
            <div className="sticky top-6 border border-[#E5E7EB] rounded-xl shadow-sm p-4 bg-white">
              <h2 className="text-lg font-semibold mb-3 text-[#2563EB]">Categories</h2>
              <div className="space-y-2">
                {allCategories.map((cat) => (
                  <div key={cat} className="border border-[#E5E7EB] rounded-lg">
                    <Link
                      href={buildCategoryHref(cat)}
                      className={`w-full flex items-center justify-between px-3 py-2 rounded-lg transition-colors ${
                        selectedCategory === cat
                          ? "bg-[#DBEAFE] text-[#2563EB] font-semibold"
                          : "text-[#1E1E1E] hover:bg-[#EFF6FF]"
                      }`}
                    >
                      <span className="font-medium">{cat}</span>
                      <span className="text-xs text-[#2563EB] font-semibold">
                        {categoryTotals[cat] ?? 0}
                      </span>
                    </Link>
                    {selectedCategory === cat && (
                      <div className="px-3 pb-2">
                        {Object.keys(grouped[cat] || {}).sort().map((brand) => (
                          <Link
                            key={brand}
                            href={buildBrandHref(cat, brand)}
                            className={`block text-sm px-2 py-1 rounded-md transition-colors mt-1 ${
                              selectedBrand === brand
                                ? "bg-[#BFDBFE] text-[#2563EB] font-semibold"
                                : "text-[#1E1E1E] hover:bg-[#DBEAFE]"
                            }`}
                          >
                            {brand} ({(grouped[cat]?.[brand]?.length ?? 0)})
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </aside>

          {/* Products grid */}
          <main className="md:col-span-9 lg:col-span-9">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {filtered.map((p) => (
                <div
                  key={`${p.id}-${p.slug}`}
                  className="border border-[#E5E7EB] rounded-xl shadow-sm transition-transform duration-200 hover:-translate-y-1 hover:shadow-[0_16px_32px_rgba(37,99,235,0.16)] bg-white"
                >
                  <div className="p-4">
                    <div className="h-36 bg-[#F9FAFB] rounded-lg flex items-center justify-center text-4xl mb-4">
                      {CATEGORY_EMOJI[p.category] || "\u{1F4E6}"}
                    </div>
                    <div className="space-y-1">
                      <h3 className="font-medium text-[#1E1E1E] line-clamp-2">{p.name}</h3>
                      <p className="text-sm text-[#6B7280]">Brand: {p.brand}</p>
                      <p className="text-sm text-[#6B7280]">Category: {p.category}</p>
                    </div>
                    <div className="mt-4">
                      <Link
                        href={`/products/${p.slug}`}
                        className="w-full inline-flex items-center justify-center px-4 py-2 rounded-xl bg-[#2563EB] text-white font-semibold hover:bg-[#1E40AF] transition-all duration-200 hover:-translate-y-0.5 shadow-md hover:shadow-[0_4px_20px_rgba(37,99,235,0.3)]"
                      >
                        View Details
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </main>
        </div>
      </div>
    </div>
  );
}

 
















