import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";

const formatCurrency = (value) =>
  typeof value === "number"
    ? new Intl.NumberFormat("en-KE", { style: "currency", currency: "KES", minimumFractionDigits: 0 }).format(value)
    : "-";

function createDoc({
  items = [],
  customer = {},
  quoteNumber = "Q-XXXX",
  brand = "Sailand Technology LTD",
}) {
  const doc = new jsPDF();

  doc.setFontSize(18);
  doc.text(brand, 14, 18);
  doc.setFontSize(11);
  doc.text(`Quote No: ${quoteNumber}`, 14, 26);
  doc.text(`Date: ${new Date().toLocaleDateString()}`, 14, 32);

  const y0 = 42;
  doc.setFontSize(12);
  doc.text("Customer Details", 14, y0);
  doc.setFontSize(10);
  doc.text(`Name: ${customer.name || "-"}`, 14, y0 + 8);
  doc.text(`Phone: ${customer.phone || "-"}`, 14, y0 + 14);
  doc.text(`Email: ${customer.email || "-"}`, 14, y0 + 20);
  if (customer.notes) {
    doc.text(`Notes: ${customer.notes}`, 14, y0 + 26);
  }

  const tableY = customer.notes ? y0 + 36 : y0 + 30;
  const tableBody = items.map((item, idx) => {
    const qty = Number(item.qty || 1) || 1;
    const unit = typeof item.price === "number" ? item.price : 0;
    const lineTotal = unit * qty;
    return [
      idx + 1,
      item.name,
      item.brand || "-",
      item.category || "-",
      qty,
      formatCurrency(unit),
      formatCurrency(lineTotal),
    ];
  });

  autoTable(doc, {
    head: [["#", "Product", "Brand", "Category", "Qty", "Price (Ex VAT)", "Line Total"]],
    body: tableBody,
    startY: tableY,
    styles: { fontSize: 9 },
    headStyles: { fillColor: [37, 99, 235] },
  });

  const footerY = doc.lastAutoTable ? doc.lastAutoTable.finalY + 10 : 270;
  const subtotal = items.reduce((sum, item) => {
    const qty = Number(item.qty || 1) || 1;
    const unit = typeof item.price === "number" ? item.price : 0;
    return sum + unit * qty;
  }, 0);

  doc.setFontSize(10);
  doc.text(`Subtotal (Ex VAT): ${formatCurrency(subtotal)}`, 14, footerY);
  doc.setFontSize(9);
  doc.text("All prices shown are exclusive of VAT. Taxes will be applied where applicable.", 14, footerY + 8);
  doc.text("Thank you for choosing Sailand Technology LTD. Original products. Trusted service.", 14, footerY + 16);

  return doc;
}

function getFileName(quoteNumber) {
  return `${quoteNumber}.pdf`;
}

export function downloadQuotePdf(options) {
  if (typeof window === "undefined") {
    return null;
  }

  const doc = createDoc(options);
  const fileName = getFileName(options?.quoteNumber || "Q-XXXX");
  doc.save(fileName);
  return fileName;
}

export async function shareQuotePdf(options) {
  if (typeof window === "undefined" || typeof navigator === "undefined") {
    return { shared: false, file: null, fileName: null, blob: null };
  }

  const doc = createDoc(options);
  const fileName = getFileName(options?.quoteNumber || "Q-XXXX");
  const blob = doc.output("blob");
  const file =
    typeof File !== "undefined" ? new File([blob], fileName, { type: "application/pdf" }) : null;

  if (file && navigator.canShare && navigator.canShare({ files: [file] })) {
    try {
      await navigator.share({
        files: [file],
        title: options?.shareTitle || `Quote ${options?.quoteNumber || "Q-XXXX"}`,
        text:
          options?.shareText ||
          `Quote ${options?.quoteNumber || "Q-XXXX"} from Sailand Technology LTD`,
      });
      return { shared: true, file, fileName, blob };
    } catch {
      return { shared: false, file, fileName, blob };
    }
  }

  return { shared: false, file, fileName, blob };
}
