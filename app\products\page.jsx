import Link from "next/link";
import SortSelect from "./SortSelect";
import TrustedBrandsMarquee from "./TrustedBrandsMarquee";
import QuoteAddButton from "./QuoteAddButton";
import { loadProductsFromCsv, groupByCategoryAndBrand } from "../../lib/ingestProducts";
import { CATEGORY_ORDER } from "../../lib/catalogRules";
import ScrollToResults from "./ScrollToResults";

const CATEGORY_EMOJI = {
  Computers: "\u{1F4BB}",
  Accessories: "\u{1F9F0}",
  Storage: "\u{1F5C3}",
  "Printers & Consumables": "\u{1F5A8}",
  "Power & Backup": "\u{1F50C}",
  "Security / Software": "\u{1F512}",
  Audio: "\u{1F3A7}",
  "Mice & Keyboards": "\u{1F5AF}",
  "Memory & RAM": "\u{1F9E9}",
  Other: "\u{1F4E6}",
};

const SUPPORT_FEATURES = [
  {
    title: "Delivery",
    description: "Delivery timelines depend on your location.",
    icon: "\u{1F69A}",
  },
  {
    title: "Up to 1 Year Warranty",
    description: "Vendor-backed warranty on featured brands.",
    icon: "\u{2705}",
  },
  {
    title: "Secure Payment",
    description: "Pay safely via M-Pesa till 656699.",
    icon: "\u{1F4B3}",
  },
  {
    title: "Exclusive Offers",
    description: "Unlock up to 30% off on first purchase.",
    icon: "\u{1F381}",
  },
];

const CTA_CARDS = [
  {
    title: "Ugreen Docking Station",
    description: "Expand every desk setup with versatile ports and fast charging.",
    href: "/products?category=Accessories",
    accent: "from-slate-900 via-blue-900 to-blue-600",
    eyebrow: "Work Essentials",
    icon: "\u{1F50C}",
  },
  {
    title: "Kingsons Elite Backpack",
    description: "Give your fleet stylish, waterproof protection on the move.",
    href: "/products?category=Accessories&brand=Kingsons",
    accent: "from-purple-600 via-purple-500 to-indigo-500",
    eyebrow: "Carry Solutions",
    icon: "\u{1F392}",
  },
  {
    title: "MSI GeForce RTX 4060",
    description: "Unlock serious graphics for creative and gaming workloads.",
    href: "/products?category=Computers",
    accent: "from-slate-900 via-slate-800 to-slate-700",
    eyebrow: "Performance Picks",
    icon: "\u{1F3AE}",
  },
];

const HERO_POINTS = [
  { label: "Business-grade hardware", icon: "\u{1F3E2}" },
  { label: "Nationwide delivery", icon: "\u{1F30F}" },
  { label: "M-Pesa & bank payments", icon: "\u{1F4B0}" },
];

const ArrowRightIcon = (props) => (
  <svg viewBox="0 0 20 20" fill="none" aria-hidden="true" {...props}>
    <path
      d="M4 10h10m0 0-4-4m4 4-4 4"
      stroke="currentColor"
      strokeWidth="1.6"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const CloseIcon = (props) => (
  <svg viewBox="0 0 20 20" fill="none" aria-hidden="true" {...props}>
    <path
      d="M6 6l8 8m0-8-8 8"
      stroke="currentColor"
      strokeWidth="1.6"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const CheckIcon = (props) => (
  <svg viewBox="0 0 20 20" fill="none" aria-hidden="true" {...props}>
    <circle cx="10" cy="10" r="9" className="fill-cyan-100/80" />
    <path
      d="M6 10.5 8.5 13l5.5-6"
      className="stroke-cyan-700"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const SORT_OPTIONS = [
  { label: "Default sorting", value: "default" },
  { label: "Brand (A-Z)", value: "brand-asc" },
  { label: "Brand (Z-A)", value: "brand-desc" },
  { label: "Name (A-Z)", value: "name-asc" },
  { label: "Name (Z-A)", value: "name-desc" },
];

function sortProducts(products, sortValue) {
  const sorted = [...products];

  switch (sortValue) {
    case "brand-asc":
      return sorted.sort((a, b) => a.brand.localeCompare(b.brand));
    case "brand-desc":
      return sorted.sort((a, b) => b.brand.localeCompare(a.brand));
    case "name-asc":
      return sorted.sort((a, b) => a.name.localeCompare(b.name));
    case "name-desc":
      return sorted.sort((a, b) => b.name.localeCompare(a.name));
    default:
      return sorted;
  }
}

export default async function ProductsPage({ searchParams }) {
  const products = await loadProductsFromCsv();
  const grouped = groupByCategoryAndBrand(products);
  const resolvedSearchParams = (await Promise.resolve(searchParams)) ?? {};

  const selectedCategory =
    typeof resolvedSearchParams.category === "string" ? resolvedSearchParams.category : null;
  const selectedBrand =
    typeof resolvedSearchParams.brand === "string" ? resolvedSearchParams.brand : null;
  const sortValue =
    typeof resolvedSearchParams.sort === "string" ? resolvedSearchParams.sort : "default";
  const searchQuery =
    typeof resolvedSearchParams.q === "string" ? resolvedSearchParams.q.trim() : "";
  const pageParam =
    typeof resolvedSearchParams.page === "string" && !Number.isNaN(Number.parseInt(resolvedSearchParams.page))
      ? Math.max(1, Number.parseInt(resolvedSearchParams.page, 10))
      : 1;

  const categoryTotals = Object.fromEntries(
    Object.entries(grouped).map(([cat, brands]) => [
      cat,
      Object.values(brands).reduce((total, items) => total + items.length, 0),
    ])
  );

  const brandTotals = products.reduce((acc, product) => {
    if (!product.brand) return acc;
    acc[product.brand] = (acc[product.brand] || 0) + 1;
    return acc;
  }, {});

  const topBrands = Object.entries(brandTotals)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 8)
    .map(([brand]) => brand);

  const normalizedQuery = searchQuery.toLowerCase();
  const matchesQuery = (product) => {
    if (!normalizedQuery) return true;
    const haystack = `${product.name} ${product.brand} ${product.category}`.toLowerCase();
    return haystack.includes(normalizedQuery);
  };

  const filtered = products.filter((product) => {
    if (selectedCategory && selectedBrand) {
      return product.category === selectedCategory && product.brand === selectedBrand && matchesQuery(product);
    }
    if (selectedCategory) {
      return product.category === selectedCategory && matchesQuery(product);
    }
    if (selectedBrand) {
      return product.brand === selectedBrand && matchesQuery(product);
    }
    return matchesQuery(product);
  });

  const showProducts = Boolean(selectedCategory || selectedBrand || searchQuery);
  const sortedProducts = showProducts ? sortProducts(filtered, sortValue) : [];

  const pageSize = 12;
  const totalPages = sortedProducts.length > 0 ? Math.ceil(sortedProducts.length / pageSize) : 1;
  const currentPage = Math.min(pageParam, totalPages);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedProducts = sortedProducts.slice(startIndex, endIndex);

  const startCount = sortedProducts.length > 0 ? startIndex + 1 : 0;
  const endCount = Math.min(endIndex, sortedProducts.length);
  const summaryText = showProducts
    ? sortedProducts.length === 0
      ? "Showing 0 results"
      : `Showing ${startCount}-${endCount} of ${sortedProducts.length} matches${
          sortedProducts.length !== products.length ? " (filtered)" : ""
        }`
    : "Select a department to browse available products.";

  const baseParams = new URLSearchParams();
  if (selectedCategory) baseParams.set("category", selectedCategory);
  if (selectedBrand) baseParams.set("brand", selectedBrand);
  if (sortValue && sortValue !== "default") baseParams.set("sort", sortValue);
  if (searchQuery) baseParams.set("q", searchQuery);
  if (currentPage > 1) baseParams.set("page", String(currentPage));

  const withOverrides = (overrides = {}) => {
    const params = new URLSearchParams(baseParams);
    Object.entries(overrides).forEach(([key, value]) => {
      if (value === null) {
        params.delete(key);
      } else {
        params.set(key, value);
      }
    });
    const query = params.toString();
    return query ? `/products?${query}` : "/products";
  };

  const buildCategoryHref = (category) => {
    if (selectedCategory === category && !selectedBrand) {
      return withOverrides({ category: null, page: null });
    }
    return withOverrides({ category, brand: null, page: null });
  };

  const buildBrandHref = (category, brand) => {
    if (selectedCategory === category && selectedBrand === brand) {
      return withOverrides({ brand: null, page: null });
    }
    return withOverrides({ category, brand, page: null });
  };

  const buildBrandBadgeHref = (brand) => {
    if (selectedBrand === brand && !selectedCategory) {
      return withOverrides({ brand: null, page: null });
    }
    return withOverrides({ brand, category: null, page: null });
  };

  const activeFilters = [];
  if (selectedCategory) {
    activeFilters.push({
      label: selectedCategory,
      href: withOverrides({ category: null, brand: null }),
    });
  }
  if (selectedBrand) {
    activeFilters.push({
      label: selectedBrand,
      href: withOverrides({ brand: null }),
    });
  }
  if (searchQuery) {
    activeFilters.push({
      label: `Search: "${searchQuery}"`,
      href: withOverrides({ q: null }),
    });
  }

  const clearFiltersHref = withOverrides({ category: null, brand: null, q: null });
  const allCategories = CATEGORY_ORDER;
  const categoryCount = Object.keys(grouped).length;
  const brandCount = Object.keys(brandTotals).length;

  const premiumStats = [
    { label: "Active Categories", value: categoryCount },
    { label: "Brands Stocked", value: brandCount },
    { label: "Avg. Response Time", value: "< 2 hrs" },
  ];

  const topBrandItems = topBrands.map((brand) => ({
    name: brand,
    href: buildBrandBadgeHref(brand),
  }));

  const buildPageHref = (page) => withOverrides({ page });
  const paginationNumbers = (() => {
    const windowSize = 3; // how many pages to show around current
    if (totalPages <= 7) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    const start = Math.max(1, currentPage - windowSize);
    const end = Math.min(totalPages, currentPage + windowSize);
    const pages = [];

    // always include current window
    for (let p = start; p <= end; p += 1) pages.push(p);

    // prepend first pages if we are near the start
    if (start > 2) {
      pages.unshift("...");
      pages.unshift(1);
    } else if (start === 2) {
      pages.unshift(1);
    }

    // append trailing pages
    if (end < totalPages - 1) {
      pages.push("...");
      pages.push(totalPages);
    } else if (end === totalPages - 1) {
      pages.push(totalPages);
    }

    return pages;
  })();

  return (
    <div className="min-h-screen bg-transparent">
      <ScrollToResults searchQuery={searchQuery} />
      <div className="mx-auto max-w-6xl space-y-8 px-4 py-10 sm:px-6 lg:px-8">
        <section className="grid gap-8 lg:[grid-template-columns:220px_minmax(0,1fr)]">
          <aside className="space-y-4">
            <div className="glass-card rounded-2xl border border-white/60 bg-white/70 shadow-sm">
              <h2 className="border-b border-slate-100 px-4 py-4 text-sm font-semibold uppercase tracking-wide text-slate-600">
                All Departments
              </h2>
              <nav className="space-y-1 px-3.5 py-4 text-slate-700">
                {allCategories.map((category) => {
                  const isActive = selectedCategory === category;
                  const count = categoryTotals[category] ?? 0;
                  return (
                    <div key={category} className="space-y-1">
                      <Link
                        prefetch={false}
                        href={buildCategoryHref(category)}
                        className={`flex items-center justify-between rounded-lg px-3 py-2 text-sm transition ${
                          isActive
                            ? "bg-cyan-50 text-cyan-700 ring-1 ring-inset ring-cyan-300 shadow-sm"
                            : "hover:bg-slate-50 focus-visible:ring-1 focus-visible:ring-cyan-300"
                        }`}
                      >
                        <span className="font-medium">{category}</span>
                        <span className="text-xs text-slate-400">{count}</span>
                      </Link>
                      {isActive && (
                        <div className="rounded-xl bg-slate-50/80 p-2 pl-3">
                          {(Object.keys(grouped[category] || {}).length === 0
                            ? []
                            : Object.keys(grouped[category] || {}).sort((a, b) => a.localeCompare(b))
                          ).map((brand) => (
                            <Link
                              key={brand}
                              prefetch={false}
                              href={buildBrandHref(category, brand)}
                              className={`block rounded-lg px-3 py-1.5 text-sm transition ${
                                selectedBrand === brand
                                  ? "bg-cyan-100 text-cyan-800 shadow-sm"
                                  : "text-slate-600 hover:bg-white focus-visible:ring-1 focus-visible:ring-cyan-300"
                              }`}
                            >
                              {brand} ({(grouped[category]?.[brand]?.length ?? 0)})
                            </Link>
                          ))}
                        </div>
                      )}
                    </div>
                  );
                })}
              </nav>
            </div>

            <div className="rounded-2xl border border-cyan-200/60 bg-gradient-to-br from-blue-600 to-blue-500 p-6 text-white shadow">
              <p className="text-xs font-semibold uppercase tracking-[0.4em] text-white/75">
                Need Assistance?
              </p>
              <h3 className="mt-3 text-lg font-semibold leading-tight">Talk to a procurement specialist.</h3>
              <p className="mt-3 text-sm text-white/85">
                We help teams assemble the perfect ICT stack for modern workplaces, education and retail.
              </p>
              <a
                href="https://api.whatsapp.com/send?phone=254704556107"
                target="_blank"
                rel="noopener noreferrer"
                className="mt-5 inline-flex items-center justify-center gap-2 rounded-full bg-cyan-400 px-5 py-2 text-sm font-semibold text-slate-900 shadow-sm transition hover:bg-cyan-300 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-cyan-100"
              >
                Chat on WhatsApp
              </a>
              <p className="mt-3 text-xs uppercase tracking-[0.3em] text-white/70">
                Weekdays 8:00am - 5:30pm
              </p>
            </div>
          </aside>

          <div className="space-y-6">
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-700 via-blue-600 to-blue-500 px-6 py-6 text-white shadow">
              <div className="absolute -right-14 -top-14 h-48 w-48 rounded-full bg-blue-400/30 blur-3xl" />
              <div className="absolute -bottom-12 right-6 h-36 w-36 rounded-full bg-white/10 blur-2xl" />
              <div className="relative">
                <p className="text-xs font-semibold uppercase tracking-[0.5em] text-white/70">
                  Product Catalogue
                </p>
                <h1 className="mt-3 text-3xl font-semibold leading-tight text-white sm:text-[34px]">
                  Shop premium ICT hardware with Sailand Technology
                </h1>
                <p className="mt-4 max-w-2xl text-sm text-white/85">
                  Discover laptops, peripherals, storage, conferencing gear, audio experiences and more—curated for teams
                  that need quality, reliability and local support in Kenya.
                </p>
                <div className="mt-5 grid gap-3 sm:grid-cols-3">
                  {HERO_POINTS.map((point) => (
                    <div
                      key={point.label}
                      className="flex items-center gap-3 rounded-2xl bg-white/15 px-3.5 py-3 text-sm backdrop-blur"
                    >
                      <CheckIcon className="h-5 w-5 shrink-0" />
                      <span className="font-medium text-white/90">{point.label}</span>
                    </div>
                  ))}
                </div>
                <div className="mt-6 flex flex-wrap items-center gap-3">
                  <Link
                    href="/contact"
                    className="glass-button inline-flex items-center justify-center gap-2 rounded-full px-6 py-2 text-sm font-semibold text-white shadow-[0_12px_30px_rgba(56,189,248,0.35)] transition hover:brightness-110 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white/70"
                  >
                    Request A Quote
                    <ArrowRightIcon className="h-4 w-4" />
                  </Link>
                  <Link
                    href="/products"
                    prefetch={false}
                    className="inline-flex items-center justify-center gap-2 rounded-full border border-white/50 bg-white/15 px-6 py-2 text-sm font-semibold text-white shadow-sm transition hover:bg-white/25 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white/70"
                  >
                    Browse All Products
                    <ArrowRightIcon className="h-4 w-4" />
                  </Link>
                </div>
              </div>
            </div>

            <div className="grid gap-4 sm:grid-cols-3">
              {premiumStats.map((stat) => (
                <div
                  key={stat.label}
                  className="glass-card rounded-2xl border border-white/60 bg-white/70 px-5 py-4 text-slate-800 shadow-sm transition hover:-translate-y-0.5 hover:shadow-lg"
                >
                  <p className="text-xs font-semibold uppercase tracking-[0.3em] text-cyan-600">{stat.label}</p>
                  <p className="mt-2 text-2xl font-semibold text-slate-900">{stat.value}</p>
                </div>
              ))}
            </div>

            <TrustedBrandsMarquee items={topBrandItems} selectedBrand={selectedBrand} />

            <div
              id="products-section"
              className="glass-card rounded-2xl border border-white/60 bg-white/70 p-6 text-slate-800 shadow-sm lg:p-7"
            >
              <div className="flex flex-col gap-6 lg:flex-row lg:items-center lg:justify-between">
                <div>
                  <p className="text-xs font-semibold uppercase tracking-[0.4em] text-cyan-600">
                    Catalogue Overview
                  </p>
                  <h2 className="mt-2 text-3xl font-semibold text-slate-900">Available products</h2>
                  <p className="mt-2 text-sm text-slate-600">{summaryText}</p>
                </div>
                <div className="w-full max-w-xs">
                  <label
                    htmlFor="sort"
                    className="block text-xs font-semibold uppercase tracking-wide text-slate-500"
                  >
                    Sort Order
                  </label>
                  <SortSelect options={SORT_OPTIONS} value={sortValue} />
                </div>
              </div>

              {activeFilters.length > 0 && (
                <div className="mt-6 flex flex-wrap items-center gap-3 rounded-2xl border border-cyan-200 bg-cyan-50 px-4 py-3 text-sm text-cyan-800">
                  <span className="font-semibold">Active filters:</span>
                  {activeFilters.map((filter) => (
                    <Link
                      key={filter.label}
                      href={filter.href}
                      className="inline-flex items-center gap-2 rounded-full bg-white px-3 py-1 font-medium text-cyan-700 shadow-sm transition hover:bg-cyan-100"
                    >
                      {filter.label}
                      <CloseIcon className="h-3.5 w-3.5 text-cyan-500" />
                    </Link>
                  ))}
                  <Link
                    href={clearFiltersHref}
                    className="ml-auto text-xs font-semibold uppercase tracking-wide text-cyan-600 underline-offset-2 hover:underline focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-cyan-400"
                  >
                    Clear all
                  </Link>
                </div>
              )}

              {!showProducts ? (
                <div className="mt-10 rounded-2xl border border-dashed border-cyan-200 bg-cyan-50 p-12 text-center text-slate-800">
                  <h3 className="text-xl font-semibold">Choose a department to get started.</h3>
                  <p className="mt-2 text-sm text-slate-600">
                    Pick any category on the left to reveal matching products and refine by brand if needed.
                  </p>
                </div>
              ) : sortedProducts.length === 0 ? (
                <div className="mt-10 rounded-2xl border border-dashed border-cyan-200 bg-cyan-50 p-12 text-center text-slate-800">
                  <h3 className="text-xl font-semibold">We could not find any matches.</h3>
                  <p className="mt-2 text-sm text-slate-600">
                    Try broadening your filters or select a different brand within this department.
                  </p>
                  <Link
                    href={clearFiltersHref}
                    className="mt-6 inline-flex items-center rounded-full bg-cyan-400 px-6 py-2 text-sm font-semibold text-slate-900 shadow-sm transition hover:bg-cyan-300"
                  >
                    Clear filters
                  </Link>
                </div>
            ) : (
                <div className="mt-8 space-y-8">
                  <div className="grid gap-6 sm:grid-cols-2 xl:grid-cols-3">
                  {paginatedProducts.map((product) => {
                    const icon = CATEGORY_EMOJI[product.category] || "\u{1F4E6}";

                    return (
                      <div
                        key={`${product.id}-${product.slug}`}
                        className="group flex h-full flex-col overflow-hidden rounded-2xl border border-slate-100 bg-gradient-to-br from-white to-slate-50 text-slate-800 shadow-sm transition duration-200 hover:-translate-y-1 hover:shadow-lg"
                      >
                    <div className="relative flex items-center justify-center bg-slate-50 px-6 py-12">
                      <span className="text-5xl">{icon}</span>
                      <span className="absolute left-4 top-4 rounded-full bg-white/95 px-3 py-1 text-xs font-semibold uppercase tracking-wide text-cyan-700 shadow-sm">
                        {product.category}
                      </span>
                    </div>
                        <div className="flex flex-1 flex-col gap-3 px-6 py-5">
                          <h3 className="line-clamp-2 text-lg font-semibold text-slate-900">{product.name}</h3>
                          <p className="text-sm text-slate-500">
                            Brand: <span className="font-medium text-slate-700">{product.brand}</span>
                          </p>
                          <p className="text-xs uppercase tracking-wide text-slate-400">
                            Catalogue Sheet: {product.sheet || "Not specified"}
                          </p>
                          <div className="mt-auto flex flex-wrap items-center gap-3 pt-4">
                            <Link
                              href={`/products/${product.slug}`}
                              className="glass-button inline-flex items-center justify-center gap-2 rounded-full px-5 py-2 text-sm font-semibold whitespace-nowrap shadow-[0_10px_22px_rgba(37,99,235,0.28)] transition hover:brightness-110 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-cyan-400"
                            >
                              View Details
                              <ArrowRightIcon className="h-4 w-4" />
                            </Link>
                            <QuoteAddButton product={product} />
                          </div>
                        </div>
                      </div>
                    );
                  })}
                  </div>

                  {totalPages > 1 && (
                    <div className="flex flex-wrap items-center justify-center gap-2">
                      <Link
                        href={currentPage > 1 ? buildPageHref(currentPage - 1) : "#"}
                        aria-disabled={currentPage === 1}
                        className={`rounded-full px-3 py-1.5 text-sm font-semibold ${
                          currentPage === 1
                            ? "cursor-not-allowed bg-slate-100 text-slate-400"
                            : "bg-white text-cyan-700 ring-1 ring-cyan-200 hover:bg-cyan-50"
                        }`}
                      >
                        Previous
                      </Link>
                      {paginationNumbers.map((p, idx) =>
                        p === "..." ? (
                          <span key={`ellipsis-${idx}`} className="px-2 text-sm font-semibold text-slate-400">
                            ...
                          </span>
                        ) : (
                          <Link
                            key={p}
                            href={buildPageHref(p)}
                            className={`rounded-full px-3 py-1.5 text-sm font-semibold ${
                              p === currentPage
                                ? "bg-cyan-600 text-white shadow"
                                : "bg-white text-cyan-700 ring-1 ring-cyan-200 hover:bg-cyan-50"
                            }`}
                          >
                            {p}
                          </Link>
                        )
                      )}
                      <Link
                        href={currentPage < totalPages ? buildPageHref(currentPage + 1) : "#"}
                        aria-disabled={currentPage === totalPages}
                        className={`rounded-full px-3 py-1.5 text-sm font-semibold ${
                          currentPage === totalPages
                            ? "cursor-not-allowed bg-slate-100 text-slate-400"
                            : "bg-white text-cyan-700 ring-1 ring-cyan-200 hover:bg-cyan-50"
                        }`}
                      >
                        Next
                      </Link>
                    </div>
                  )}
                </div>
              )}
            </div>

            <div className="grid gap-4 text-slate-800 sm:grid-cols-2 xl:grid-cols-4">
              {SUPPORT_FEATURES.map((feature) => (
                <div
                  key={feature.title}
                  className="glass-card rounded-2xl border border-white/60 bg-white/80 p-5 shadow-sm transition hover:-translate-y-0.5 hover:shadow-lg"
                >
                  <div className="flex items-start gap-3">
                    <span className="text-2xl">{feature.icon}</span>
                    <div>
                      <h3 className="text-sm font-semibold text-slate-900">{feature.title}</h3>
                      <p className="mt-1 text-sm text-slate-600">{feature.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="grid gap-5 text-white md:grid-cols-2 xl:grid-cols-3">
              {CTA_CARDS.map((card) => (
                <div
                  key={card.title}
                  className={`relative overflow-hidden rounded-2xl bg-gradient-to-br ${card.accent} p-6 shadow`}
                >
                  <div className="absolute inset-0 bg-[radial-gradient(circle_at_top,_rgba(255,255,255,0.18),_transparent_55%)]" />
                  <div className="relative flex h-full flex-col">
                    <span className="text-4xl">{card.icon}</span>
                    <p className="mt-4 text-xs font-semibold uppercase tracking-[0.4em] text-white/70">
                      {card.eyebrow}
                    </p>
                    <h3 className="mt-2 text-xl font-semibold text-white/95">{card.title}</h3>
                    <p className="mt-3 text-sm text-white/85">{card.description}</p>
                    <Link
                      href={card.href}
                      prefetch={false}
                      className="mt-auto inline-flex items-center gap-2 text-sm font-semibold text-white transition hover:text-cyan-100 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white/70"
                    >
                      Learn more
                      <ArrowRightIcon className="h-4 w-4" />
                    </Link>
                  </div>
                </div>
              ))}
            </div>

            <div className="rounded-2xl border border-slate-100 bg-white p-6 text-slate-800 shadow-sm lg:p-8">
              <div className="grid gap-6 lg:grid-cols-[2fr,1fr] lg:items-center">
                <div>
                  <p className="text-xs font-semibold uppercase tracking-[0.4em] text-cyan-600">Why sail with us</p>
                  <h2 className="mt-3 text-2xl font-semibold text-slate-900">
                    Kenya&apos;s trusted source for quality tech products
                  </h2>
                  <p className="mt-4 text-sm text-slate-600">
                    Discover premium laptops, docking stations, conferencing kits, gaming gear, storage and accessories
                    from leading global brands. We deliver across Kenya with dedicated support and flexible procurement
                    via Sailand Technology LTD.
                  </p>
                </div>
                <div className="flex flex-col items-start gap-3 lg:items-end">
                  <Link
                    href="/products"
                    className="inline-flex items-center justify-center gap-2 rounded-full bg-cyan-400 px-6 py-2 text-sm font-semibold text-slate-900 shadow-sm transition hover:bg-cyan-300 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-cyan-400"
                  >
                    Browse Products
                    <ArrowRightIcon className="h-4 w-4" />
                  </Link>
                  <Link
                    href="/contact"
                    className="inline-flex items-center justify-center gap-2 rounded-full border border-cyan-400 px-6 py-2 text-sm font-semibold text-slate-900 transition hover:bg-cyan-100 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-cyan-400"
                  >
                    Contact Our Team
                    <ArrowRightIcon className="h-4 w-4" />
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
