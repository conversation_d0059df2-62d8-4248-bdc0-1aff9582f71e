import "./globals.css";
import { Inter } from "next/font/google";
import QuoteProvider from "./context/QuoteContext";
import Layout from "./components/Layout";

const inter = Inter({ subsets: ["latin"] });

export const metadata = {
  title: "Sailand Technology LTD",
  description: "Kenya's trusted ICT and electronics supplier.",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <QuoteProvider>
          <Layout>{children}</Layout>
        </QuoteProvider>
      </body>
    </html>
  );
}
